<UserControl x:Class="SFDSystem.Views.StaticReportTemplate"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft"
             Background="White">
    
    <Grid Width="794" Height="1123" Background="White">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- العنوان الرئيسي -->
        <Border Grid.Row="0" BorderBrush="Black" BorderThickness="2" Margin="20" Padding="10">
            <TextBlock Text="محضر استدراج عروض أسعار" 
                       FontSize="18" 
                       FontWeight="Bold" 
                       HorizontalAlignment="Center" 
                       VerticalAlignment="Center"/>
        </Border>
        
        <!-- معلومات أساسية -->
        <StackPanel Grid.Row="1" Margin="20,10">
            <TextBlock Text="التاريخ: 24/06/2025" FontSize="12" Margin="0,5"/>
            <TextBlock Text="رقم الزيارة: 22" FontSize="12" Margin="0,5"/>
            <TextBlock Text="القطاع: القطاع الميداني" FontSize="12" Margin="0,5"/>
        </StackPanel>
        
        <!-- جدول العروض -->
        <Border Grid.Row="2" BorderBrush="Black" BorderThickness="2" Margin="20,10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="40"/>
                    <RowDefinition Height="35"/>
                    <RowDefinition Height="35"/>
                    <RowDefinition Height="35"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="60"/>
                    <ColumnDefinition Width="200"/>
                    <ColumnDefinition Width="120"/>
                    <ColumnDefinition Width="100"/>
                    <ColumnDefinition Width="120"/>
                </Grid.ColumnDefinitions>
                
                <!-- رأس الجدول -->
                <Border Grid.Row="0" Grid.Column="0" BorderBrush="Black" BorderThickness="1" Background="#4682B4">
                    <TextBlock Text="الرقم" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="1" BorderBrush="Black" BorderThickness="1" Background="#4682B4">
                    <TextBlock Text="اسم السائق" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="2" BorderBrush="Black" BorderThickness="1" Background="#4682B4">
                    <TextBlock Text="رقم التلفون" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="3" BorderBrush="Black" BorderThickness="1" Background="#4682B4">
                    <TextBlock Text="السعر المقدم" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="0" Grid.Column="4" BorderBrush="Black" BorderThickness="1" Background="#4682B4">
                    <TextBlock Text="الحالة" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                
                <!-- الصف الأول -->
                <Border Grid.Row="1" Grid.Column="0" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="1" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="1" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="فيصل حميد أحمد الطيبي" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="2" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="775097743" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="3" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="20,000 ريال" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="1" Grid.Column="4" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="🏆 فائز" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                
                <!-- الصف الثاني -->
                <Border Grid.Row="2" Grid.Column="0" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="2" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="1" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="محمد عبده علي محمد عايض" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="2" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="733170552" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="3" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="13,500 ريال" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="2" Grid.Column="4" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="مقبول" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                
                <!-- الصف الثالث -->
                <Border Grid.Row="3" Grid.Column="0" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="3" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="1" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="أحمد محمد علي" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="2" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="712345678" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="3" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="25,000 ريال" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
                <Border Grid.Row="3" Grid.Column="4" BorderBrush="Black" BorderThickness="1">
                    <TextBlock Text="مرفوض" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                </Border>
            </Grid>
        </Border>
        
        <!-- ملاحظات -->
        <StackPanel Grid.Row="3" Margin="20" VerticalAlignment="Bottom">
            <TextBlock Text="ملاحظات:" FontWeight="Bold" Margin="0,10,0,5"/>
            <TextBlock Text="تم اختيار العرض الأول كأفضل عرض من ناحية السعر والجودة." 
                       TextWrapping="Wrap" Margin="0,0,0,20"/>
            
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                    <TextBlock Text="المكلف بالمهمة" FontWeight="Bold"/>
                    <TextBlock Text="عبدالله علي ناصر الإدريسي"/>
                    <TextBlock Text="- غالب عبدالله علي عدية"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                    <TextBlock Text="مسئول الحركة" FontWeight="Bold"/>
                    <TextBlock Text="علي علي الحمدي"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                    <TextBlock Text="يعتمد" FontWeight="Bold"/>
                    <TextBlock Text="م/محمد محمد الرياحي"/>
                    <TextBlock Text="مدير الفرع"/>
                </StackPanel>
            </Grid>
        </StackPanel>
    </Grid>
</UserControl>
