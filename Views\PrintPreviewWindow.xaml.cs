using System;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using Microsoft.Win32;
using System.Printing;
using iTextSharp.text;
using iTextSharp.text.pdf;
using System.Windows.Markup;
using SFDSystem.Views;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Documents;
using PdfParagraph = iTextSharp.text.Paragraph;

namespace DriverManagementSystem.Views
{
    public partial class PrintPreviewWindow : Window
    {
        private FrameworkElement _reportContent;

        public PrintPreviewWindow(FrameworkElement reportContent)
        {
            InitializeComponent();
            _reportContent = reportContent;
            LoadPreview();
        }

        private void LoadPreview()
        {
            try
            {
                // Clone the report content for preview
                var clonedContent = CloneReportContent(_reportContent);
                ReportContentPresenter.Content = clonedContent;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل المعاينة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private FrameworkElement CloneReportContent(FrameworkElement original)
        {
            // Preview dimensions - Full size for better visibility
            double previewWidth = 780;   // Larger width to prevent cropping
            double previewHeight = 1080; // Full height

            // Create a new ReportView for preview
            var clonedReport = new ReportView
            {
                DataContext = original.DataContext,
                Width = previewWidth,
                Height = previewHeight,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch
            };

            // Force layout update for preview
            clonedReport.Measure(new Size(previewWidth, previewHeight));
            clonedReport.Arrange(new Rect(0, 0, previewWidth, previewHeight));
            clonedReport.UpdateLayout();

            return clonedReport;
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                PrintReport();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void SavePdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SaveAsPdf();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void PrintReport()
        {
            PrintDialog printDialog = new PrintDialog();

            // محاولة تحديد Microsoft Print to PDF كطابعة افتراضية
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ البحث عن Microsoft Print to PDF...");

                // البحث عن Microsoft Print to PDF في قائمة الطابعات
                var printQueues = new System.Printing.LocalPrintServer().GetPrintQueues();
                var microsoftPrintToPdf = printQueues.FirstOrDefault(pq =>
                    pq.Name.Contains("Microsoft Print to PDF", StringComparison.OrdinalIgnoreCase));

                if (microsoftPrintToPdf != null)
                {
                    printDialog.PrintQueue = microsoftPrintToPdf;
                    System.Diagnostics.Debug.WriteLine("✅ تم تحديد Microsoft Print to PDF كطابعة افتراضية");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على Microsoft Print to PDF، سيتم عرض جميع الطابعات");
                }

                // تحديد A4 مسبقاً
                var a4Size = new System.Printing.PageMediaSize(System.Printing.PageMediaSizeName.ISOA4, 210, 297);
                printDialog.PrintTicket.PageMediaSize = a4Size;
                printDialog.PrintTicket.PageOrientation = System.Printing.PageOrientation.Portrait;

                System.Diagnostics.Debug.WriteLine("✅ تم تحديد A4 للطابعة المختارة في PrintPreview");
            }
            catch (Exception preEx)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ تعذر تحديد الطابعة في PrintPreview: {preEx.Message}");
            }

            if (printDialog.ShowDialog() == true)
            {
                // تحديد إعدادات الطباعة A4 بقوة
                try
                {
                    // تحديد حجم الصفحة A4 بطرق متعددة لضمان التطبيق
                    if (printDialog.PrintTicket != null)
                    {
                        printDialog.PrintTicket.PageMediaSize = new System.Printing.PageMediaSize(
                            System.Printing.PageMediaSizeName.ISOA4, 210, 297);
                        printDialog.PrintTicket.PageOrientation = System.Printing.PageOrientation.Portrait;

                        // تأكيد إضافي لحجم A4
                        var a4Size = new System.Printing.PageMediaSize(System.Printing.PageMediaSizeName.ISOA4);
                        printDialog.PrintTicket.PageMediaSize = a4Size;

                        System.Diagnostics.Debug.WriteLine("✅ تم تحديد إعدادات الطباعة A4 بقوة في PrintPreview");
                    }
                }
                catch (Exception pageEx)
                {
                    System.Diagnostics.Debug.WriteLine($"⚠️ تعذر تحديد إعدادات الطباعة في PrintPreview: {pageEx.Message}");

                    // محاولة بديلة لتحديد A4
                    try
                    {
                        printDialog.PrintQueue.DefaultPrintTicket.PageMediaSize =
                            new System.Printing.PageMediaSize(System.Printing.PageMediaSizeName.ISOA4);
                        System.Diagnostics.Debug.WriteLine("✅ تم تحديد A4 عبر PrintQueue في PrintPreview");
                    }
                    catch (Exception fallbackEx)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ فشل في الطريقة البديلة في PrintPreview: {fallbackEx.Message}");
                    }
                }

                try
                {
                    System.Diagnostics.Debug.WriteLine($"🖨️ بدء الطباعة باستخدام: {printDialog.PrintQueue.Name}");

                    // إذا كان المستخدم اختار Microsoft Print to PDF، استخدم النظام المحسن
                    if (printDialog.PrintQueue.Name.Contains("Microsoft Print to PDF", StringComparison.OrdinalIgnoreCase))
                    {
                        System.Diagnostics.Debug.WriteLine("📄 المستخدم اختار Microsoft Print to PDF، سيتم استخدام النظام المحسن...");

                        SaveFileDialog saveDialog = new SaveFileDialog
                        {
                            Filter = "PDF Files (*.pdf)|*.pdf",
                            DefaultExt = "pdf",
                            FileName = $"محضر_استخراج_عروض_الأسعار_{DateTime.Now:yyyy-MM-dd_HH-mm}.pdf"
                        };

                        if (saveDialog.ShowDialog() == true)
                        {
                            // الحصول على البيانات الأصلية من ViewModel
                            var pdfReportViewModel = _reportContent.DataContext as ViewModels.ReportViewModel;
                            if (pdfReportViewModel?.ReportData != null)
                            {
                                CreateDataBasedPdf(pdfReportViewModel.ReportData, saveDialog.FileName);
                                MessageBox.Show($"تم حفظ PDF بنجاح باستخدام Microsoft Print to PDF!\n\n📁 المسار: {saveDialog.FileName}\n📐 المقاس: A4 (210×297 مم)\n🖨️ جودة عالية مع البيانات الأصلية\n📄 صفحات متعددة في ملف واحد", "نجح الحفظ",
                                    MessageBoxButton.OK, MessageBoxImage.Information);
                            }
                            else
                            {
                                MessageBox.Show("لا توجد بيانات تقرير متاحة للحفظ", "خطأ",
                                    MessageBoxButton.OK, MessageBoxImage.Error);
                            }
                        }
                        return;
                    }

                    // للطابعات العادية، استخدم النظام المحسن
                    System.Diagnostics.Debug.WriteLine("🖨️ استخدام النظام المحسن للطابعات العادية...");

                    // التحقق من البيانات قبل الطباعة
                    var reportViewModel = _reportContent.DataContext as ViewModels.ReportViewModel;
                    if (reportViewModel?.ReportData == null)
                    {
                        MessageBox.Show("لا توجد بيانات تقرير متاحة للطباعة", "خطأ",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return;
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ البيانات متوفرة: {reportViewModel.ReportData.PriceOffers.Count} عرض سعر");

                    // إنشاء مستند طباعة متعدد الصفحات
                    var multiPageDocument = CreateMultiPagePrintDocument(reportViewModel);
                    if (multiPageDocument != null)
                    {
                        printDialog.PrintDocument(multiPageDocument.DocumentPaginator, "تقرير الزيارة الميدانية - متعدد الصفحات");
                        MessageBox.Show($"تم إرسال التقرير للطباعة بنجاح!\n📄 البيانات: {reportViewModel.ReportData.PriceOffers.Count} عرض سعر\n📄 عدد الصفحات: {multiPageDocument.Pages.Count}\n🖨️ الطابعة: {printDialog.PrintQueue.Name}\n📐 حجم الطباعة: A4", "طباعة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return;
                    }

                    // النظام التقليدي كبديل
                    var printDocument = CreateCompletePrintDocument(_reportContent);
                    if (printDocument != null)
                    {
                        printDialog.PrintVisual(printDocument, "تقرير الزيارة الميدانية - مستند كامل");
                        var reportPages = FindReportPages(_reportContent);
                        var pageCount = reportPages?.Count ?? 1;
                        MessageBox.Show($"تم إرسال التقرير للطباعة بنجاح!\n📄 عدد الصفحات: {pageCount}\n🖨️ الطابعة: {printDialog.PrintQueue.Name}\n📐 حجم الطباعة: A4", "طباعة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        var printVersion = CreatePrintVersion();
                        printDialog.PrintVisual(printVersion, "تقرير الزيارة الميدانية");
                        MessageBox.Show($"تم إرسال التقرير للطباعة بنجاح!\n🖨️ الطابعة: {printDialog.PrintQueue.Name}", "طباعة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في نظام الطباعة: {ex.Message}");

                    // في حالة الخطأ، استخدم الطريقة الأساسية
                    try
                    {
                        var printVersion = CreatePrintVersion();
                        printDialog.PrintVisual(printVersion, "تقرير الزيارة الميدانية");

                        MessageBox.Show("تم إرسال التقرير للطباعة بنجاح!\n📋 تحقق من طابعتك لاستلام المستندات.", "طباعة",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    catch (Exception fallbackEx)
                    {
                        MessageBox.Show($"خطأ في الطباعة: {fallbackEx.Message}\n\nتأكد من أن الطابعة متصلة وتعمل بشكل صحيح.", "خطأ في الطباعة",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
        }

        private FrameworkElement CreatePrintVersion()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ إنشاء نسخة محسنة للطباعة مع ضمان ظهور البيانات...");

                // A4 exact dimensions for printing
                double a4Width = 794;   // 8.27 inches × 96 DPI
                double a4Height = 1123; // 11.69 inches × 96 DPI

                // الحصول على البيانات من ViewModel
                var reportViewModel = _reportContent.DataContext as ViewModels.ReportViewModel;
                if (reportViewModel?.ReportData == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ لا توجد بيانات في ReportViewModel");
                    return CreateFallbackPrintVersion(a4Width, a4Height);
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم العثور على البيانات: {reportViewModel.ReportData.PriceOffers.Count} عرض سعر");

                // Create full-size container for printing
                var printContainer = new Border
                {
                    Background = Brushes.White,
                    Width = a4Width,
                    Height = a4Height,
                    BorderThickness = new Thickness(0),
                    FlowDirection = FlowDirection.RightToLeft
                };

                // Create full-size report content with forced data binding
                var printReport = new ReportView
                {
                    DataContext = reportViewModel, // تأكد من ربط البيانات
                    Width = a4Width - 40,  // Account for margins
                    Height = a4Height - 40,
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    VerticalAlignment = VerticalAlignment.Stretch,
                    FlowDirection = FlowDirection.RightToLeft
                };

                printContainer.Child = printReport;

                // Force complete layout update with data binding
                printContainer.Measure(new Size(a4Width, a4Height));
                printContainer.Arrange(new Rect(0, 0, a4Width, a4Height));
                printContainer.UpdateLayout();

                // إجبار تحديث البيانات في العناصر الفرعية
                ForceDataRefresh(printReport);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء نسخة الطباعة بنجاح مع البيانات");
                return printContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء نسخة الطباعة: {ex.Message}");
                return CreateFallbackPrintVersion(794, 1123);
            }
        }

        /// <summary>
        /// إجبار تحديث البيانات في جميع العناصر الفرعية
        /// </summary>
        private void ForceDataRefresh(FrameworkElement element)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء إجبار تحديث البيانات...");

                // التحقق من وجود DataContext
                if (element.DataContext is ViewModels.ReportViewModel viewModel)
                {
                    System.Diagnostics.Debug.WriteLine($"📊 البيانات المتوفرة: {viewModel.ReportData?.PriceOffers?.Count ?? 0} عرض سعر");

                    // إجبار تحديث PropertyChanged للبيانات الرئيسية
                    if (viewModel.ReportData != null)
                    {
                        // إجبار إعادة تحميل البيانات
                        var tempOffers = viewModel.ReportData.PriceOffers.ToList();
                        viewModel.ReportData.PriceOffers.Clear();

                        foreach (var offer in tempOffers)
                        {
                            viewModel.ReportData.PriceOffers.Add(offer);
                        }

                        System.Diagnostics.Debug.WriteLine($"✅ تم إعادة تحميل {tempOffers.Count} عرض سعر");
                    }
                }

                // البحث عن جميع ItemsControl في العنصر
                var itemsControls = FindVisualChildren<ItemsControl>(element);

                foreach (var itemsControl in itemsControls)
                {
                    // إجبار تحديث البيانات
                    var binding = BindingOperations.GetBinding(itemsControl, ItemsControl.ItemsSourceProperty);
                    if (binding != null)
                    {
                        BindingOperations.ClearBinding(itemsControl, ItemsControl.ItemsSourceProperty);
                        BindingOperations.SetBinding(itemsControl, ItemsControl.ItemsSourceProperty, binding);
                        System.Diagnostics.Debug.WriteLine($"🔗 تم إعادة ربط ItemsControl");
                    }

                    // تحديث التخطيط
                    itemsControl.UpdateLayout();
                }

                // تحديث التخطيط العام
                element.UpdateLayout();

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث {itemsControls.Count()} عنصر ItemsControl");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن العناصر الفرعية من نوع معين
        /// </summary>
        private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
        {
            if (depObj != null)
            {
                for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
                {
                    DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                    if (child != null && child is T)
                    {
                        yield return (T)child;
                    }

                    foreach (T childOfChild in FindVisualChildren<T>(child))
                    {
                        yield return childOfChild;
                    }
                }
            }
        }

        /// <summary>
        /// إنشاء نسخة احتياطية للطباعة في حالة الخطأ
        /// </summary>
        private FrameworkElement CreateFallbackPrintVersion(double width, double height)
        {
            var fallbackContainer = new Border
            {
                Background = Brushes.White,
                Width = width,
                Height = height,
                BorderThickness = new Thickness(0)
            };

            var fallbackReport = new ReportView
            {
                DataContext = _reportContent.DataContext,
                Width = width - 40,
                Height = height - 40,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch
            };

            fallbackContainer.Child = fallbackReport;

            fallbackContainer.Measure(new Size(width, height));
            fallbackContainer.Arrange(new Rect(0, 0, width, height));
            fallbackContainer.UpdateLayout();

            return fallbackContainer;
        }

        private void SaveAsPdf()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 بدء حفظ PDF باستخدام Microsoft Print to PDF...");

                // الحصول على البيانات الأصلية من ViewModel
                var reportViewModel = _reportContent.DataContext as ViewModels.ReportViewModel;
                if (reportViewModel?.ReportData == null)
                {
                    MessageBox.Show("لا توجد بيانات تقرير متاحة للحفظ", "خطأ",
                        MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // إنشاء اسم الملف
                var fileName = $"محضر_استخراج_عروض_الأسعار_{DateTime.Now:yyyy-MM-dd_HH-mm}.pdf";
                var desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                var fullPath = Path.Combine(desktopPath, fileName);

                // إنشاء PDF باستخدام البيانات الأصلية مباشرة
                CreateDataBasedPdf(reportViewModel.ReportData, fullPath);

                MessageBox.Show($"تم حفظ PDF بنجاح!\n\n📁 المسار: {fullPath}\n📄 تم إنشاء صفحات متعددة\n📐 المقاس: A4 (210×297 مم)\n🖨️ جودة عالية باستخدام البيانات الأصلية\n\n✅ الملف جاهز للطباعة أو المشاركة", "نجح الحفظ",
                    MessageBoxButton.OK, MessageBoxImage.Information);

                // Ask if user wants to open the file
                var result = MessageBox.Show("هل تريد فتح الملف الآن؟", "فتح الملف",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                    {
                        FileName = fullPath,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ PDF: {ex.Message}");
                MessageBox.Show($"خطأ في حفظ PDF: {ex.Message}\n\nتأكد من أن لديك صلاحيات الكتابة على سطح المكتب", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private List<System.Windows.Controls.Border> FindReportPages(FrameworkElement reportView)
        {
            var pages = new List<System.Windows.Controls.Border>();

            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 بدء البحث عن صفحات التقرير في PrintPreview...");

                // البحث عن StackPanel الرئيسي
                var mainStackPanel = FindChild<StackPanel>(reportView);

                if (mainStackPanel != null)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على StackPanel الرئيسي مع {mainStackPanel.Children.Count} عنصر فرعي");

                    // البحث عن جميع Border التي تحتوي على صفحات التقرير
                    foreach (var child in mainStackPanel.Children)
                    {
                        if (child is System.Windows.Controls.Border border)
                        {
                            bool isReportPage = false;

                            // طريقة 1: التحقق من Style (PrintPageStyle)
                            if (border.Style != null)
                            {
                                try
                                {
                                    var printPageStyle = border.TryFindResource("PrintPageStyle");
                                    if (printPageStyle != null && border.Style == printPageStyle)
                                    {
                                        isReportPage = true;
                                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بـ PrintPageStyle");
                                    }
                                }
                                catch
                                {
                                    var styleKey = border.Style.ToString();
                                    if (styleKey.Contains("PrintPageStyle"))
                                    {
                                        isReportPage = true;
                                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بـ Style: {styleKey}");
                                    }
                                }
                            }

                            // طريقة 2: التحقق من خلال المحتوى
                            if (!isReportPage && border.Child is StackPanel stackPanel)
                            {
                                if (stackPanel.Children.Count > 3)
                                {
                                    isReportPage = true;
                                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بالمحتوى: {stackPanel.Children.Count} عناصر");
                                }
                            }

                            // طريقة 3: التحقق من خلال الخصائص
                            if (!isReportPage)
                            {
                                var margin = border.Margin;
                                if ((margin.Top >= 5 && margin.Bottom >= 5) ||
                                    border.ActualWidth > 500 || border.ActualHeight > 600)
                                {
                                    if (border.Height != 30 &&
                                        border.Background != System.Windows.Media.Brushes.Transparent)
                                    {
                                        isReportPage = true;
                                        System.Diagnostics.Debug.WriteLine($"✅ تم العثور على صفحة تقرير بالخصائص");
                                    }
                                }
                            }

                            if (isReportPage)
                            {
                                pages.Add(border);
                            }
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"📊 إجمالي الصفحات المكتشفة في PrintPreview: {pages.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في البحث عن صفحات التقرير في PrintPreview: {ex.Message}");
            }

            return pages;
        }

        private T FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T result)
                    return result;

                var childOfChild = FindChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }



        private FrameworkElement CreateCompletePrintDocument(FrameworkElement reportView)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء مستند طباعة شامل من PrintPreview...");

                // البحث عن جميع صفحات التقرير
                var reportPages = FindReportPages(reportView);

                if (reportPages?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {reportPages.Count} صفحة");

                    // إنشاء حاوي رئيسي محسن لجميع الصفحات بحجم A4
                    var mainContainer = new StackPanel
                    {
                        Background = System.Windows.Media.Brushes.White,
                        Orientation = Orientation.Vertical,
                        Width = 816,  // عرض A4 محسن (8.5" × 96 DPI)
                        HorizontalAlignment = HorizontalAlignment.Center,
                        FlowDirection = FlowDirection.RightToLeft, // اتجاه عربي صحيح
                        Margin = new Thickness(0)
                    };

                    // إضافة كل صفحة إلى الحاوي الرئيسي
                    for (int i = 0; i < reportPages.Count; i++)
                    {
                        var page = reportPages[i];

                        // إنشاء نسخة محسنة من الصفحة للطباعة
                        var pageClone = CreateEnhancedPrintablePage(page);

                        if (pageClone != null)
                        {
                            // إضافة الصفحة إلى الحاوي
                            mainContainer.Children.Add(pageClone);

                            // إضافة فاصل صفحة إذا لم تكن الصفحة الأخيرة
                            if (i < reportPages.Count - 1)
                            {
                                var pageBreak = new Border
                                {
                                    Height = 50,
                                    Background = System.Windows.Media.Brushes.Transparent
                                };
                                mainContainer.Children.Add(pageBreak);
                            }
                        }
                    }

                    // تحديث تخطيط الحاوي
                    mainContainer.Measure(new Size(double.PositiveInfinity, double.PositiveInfinity));
                    mainContainer.Arrange(new Rect(mainContainer.DesiredSize));
                    mainContainer.UpdateLayout();

                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مستند شامل يحتوي على {reportPages.Count} صفحة من PrintPreview");
                    return mainContainer;
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لم يتم العثور على صفحات منفصلة في PrintPreview");
                    return reportView; // إرجاع التقرير الأصلي
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المستند الشامل من PrintPreview: {ex.Message}");
                return null;
            }
        }

        private FrameworkElement ClonePageForPrint(System.Windows.Controls.Border originalPage)
        {
            try
            {
                // إنشاء نسخة من الصفحة للطباعة
                var xaml = System.Windows.Markup.XamlWriter.Save(originalPage);
                var clonedPage = (FrameworkElement)System.Windows.Markup.XamlReader.Parse(xaml);

                // تطبيق خصائص الطباعة A4
                clonedPage.Width = 794;   // عرض A4 (210mm)
                clonedPage.Height = 1123; // ارتفاع A4 (297mm)
                clonedPage.HorizontalAlignment = HorizontalAlignment.Center;
                clonedPage.VerticalAlignment = VerticalAlignment.Top;

                // تحديث التخطيط
                clonedPage.Measure(new Size(794, 1123));
                clonedPage.Arrange(new Rect(0, 0, 794, 1123));
                clonedPage.UpdateLayout();

                System.Diagnostics.Debug.WriteLine($"✅ تم نسخ الصفحة بحجم A4 في PrintPreview: 794×1123");
                return clonedPage;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ فشل في نسخ الصفحة في PrintPreview: {ex.Message}");
                return originalPage; // إرجاع الصفحة الأصلية
            }
        }

        /// <summary>
        /// إنشاء PDF متعدد الصفحات باستخدام البيانات الأصلية مباشرة
        /// </summary>
        private void CreateMultiPagePdf(List<Border> pages, string fileName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"📄 بدء إنشاء PDF متعدد الصفحات مع البيانات الأصلية...");

                // الحصول على البيانات الأصلية من ViewModel
                var reportViewModel = _reportContent.DataContext as ViewModels.ReportViewModel;
                if (reportViewModel?.ReportData == null)
                {
                    throw new Exception("لا توجد بيانات تقرير متاحة للطباعة");
                }

                // إنشاء PDF باستخدام البيانات الأصلية
                CreateDataBasedPdf(reportViewModel.ReportData, fileName);

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء PDF متعدد الصفحات بنجاح: {fileName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء PDF متعدد الصفحات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء PDF باستخدام البيانات الأصلية مباشرة من قاعدة البيانات
        /// </summary>
        private void CreateDataBasedPdf(Models.ReportModel reportData, string fileName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء PDF باستخدام البيانات الأصلية...");

                // إنشاء مستند PDF بحجم A4
                var document = new iTextSharp.text.Document(PageSize.A4, 20, 20, 20, 20);

                using (var stream = new FileStream(fileName, FileMode.Create))
                {
                    var writer = PdfWriter.GetInstance(document, stream);
                    document.Open();

                    // إنشاء الخطوط العربية
                    var arabicFont = CreateArabicFont();
                    var titleFont = new iTextSharp.text.Font(arabicFont, 16, iTextSharp.text.Font.BOLD);
                    var headerFont = new iTextSharp.text.Font(arabicFont, 14, iTextSharp.text.Font.BOLD);
                    var normalFont = new iTextSharp.text.Font(arabicFont, 12, iTextSharp.text.Font.NORMAL);

                    // الصفحة الأولى: محضر استدراج عروض الأسعار
                    CreateReportPage(document, reportData, titleFont, headerFont, normalFont);

                    // الصفحات التالية: عقد إيجار السيارة
                    document.NewPage();
                    CreateContractPages(document, reportData, titleFont, headerFont, normalFont);

                    document.Close();
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء PDF بالبيانات الأصلية بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء PDF بالبيانات الأصلية: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء نسخة محسنة من الصفحة للـ PDF
        /// </summary>
        private FrameworkElement CreateEnhancedPageForPdf(Border originalPage)
        {
            try
            {
                // إنشاء حاوي بحجم A4 محسن
                var pdfContainer = new Grid
                {
                    Width = 816,  // عرض A4 محسن (8.5" × 96 DPI)
                    Height = 1056, // ارتفاع A4 محسن (11" × 96 DPI)
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // إنشاء Border داخلي مع هوامش
                var innerBorder = new Border
                {
                    Margin = new Thickness(24), // هوامش 0.25 بوصة
                    Background = Brushes.Transparent,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // نسخ محتوى الصفحة الأصلية
                if (originalPage.Parent is Panel parentPanel)
                {
                    parentPanel.Children.Remove(originalPage);
                }

                originalPage.FlowDirection = FlowDirection.RightToLeft;
                innerBorder.Child = originalPage;
                pdfContainer.Children.Add(innerBorder);

                // تحديث التخطيط
                pdfContainer.Measure(new Size(816, 1056));
                pdfContainer.Arrange(new Rect(0, 0, 816, 1056));
                pdfContainer.UpdateLayout();

                return pdfContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة محسنة للـ PDF: {ex.Message}");
                return originalPage;
            }
        }

        /// <summary>
        /// تحويل صفحة إلى صورة
        /// </summary>
        private byte[] ConvertPageToImage(FrameworkElement page)
        {
            try
            {
                // إنشاء RenderTargetBitmap بدقة عالية
                var renderBitmap = new RenderTargetBitmap(
                    (int)page.Width,
                    (int)page.Height,
                    96, 96, // DPI
                    PixelFormats.Pbgra32);

                renderBitmap.Render(page);

                // تحويل إلى PNG
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));

                using (var stream = new MemoryStream())
                {
                    encoder.Save(stream);
                    return stream.ToArray();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحويل الصفحة إلى صورة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء خط عربي للـ PDF
        /// </summary>
        private BaseFont CreateArabicFont()
        {
            try
            {
                // استخدام خط عربي مدمج
                return BaseFont.CreateFont("c:/windows/fonts/arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            }
            catch
            {
                // في حالة فشل الخط العربي، استخدم الخط الافتراضي
                return BaseFont.CreateFont(BaseFont.HELVETICA, BaseFont.CP1252, BaseFont.NOT_EMBEDDED);
            }
        }

        /// <summary>
        /// إنشاء صفحة المحضر
        /// </summary>
        private void CreateReportPage(iTextSharp.text.Document document, Models.ReportModel reportData,
            iTextSharp.text.Font titleFont, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء صفحة المحضر...");

                // العنوان الرئيسي
                var title = new PdfParagraph("محضر استدراج عروض الأسعار", titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(title);

                // معلومات التاريخ والرقم
                var dateInfo = new PdfParagraph($"التاريخ: {reportData.ReportDate} | رقم الزيارة: {reportData.VisitNumber}", headerFont)
                {
                    Alignment = Element.ALIGN_RIGHT,
                    SpacingAfter = 15
                };
                document.Add(dateInfo);

                // جدول العروض
                CreateOffersTable(document, reportData, headerFont, normalFont);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة المحضر بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة المحضر: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء جدول العروض
        /// </summary>
        private void CreateOffersTable(iTextSharp.text.Document document, Models.ReportModel reportData,
            iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            try
            {
                // إنشاء جدول بـ 3 أعمدة
                var table = new PdfPTable(3)
                {
                    WidthPercentage = 100,
                    SpacingBefore = 10,
                    SpacingAfter = 10
                };

                // تحديد عرض الأعمدة
                table.SetWidths(new float[] { 1f, 3f, 1f });

                // رؤوس الجدول
                table.AddCell(CreateTableCell("م", headerFont, true));
                table.AddCell(CreateTableCell("اسم السائق", headerFont, true));
                table.AddCell(CreateTableCell("السعر المقترح", headerFont, true));

                // إضافة بيانات العروض
                if (reportData.PriceOffers?.Any() == true)
                {
                    foreach (var offer in reportData.PriceOffers)
                    {
                        table.AddCell(CreateTableCell(offer.SerialNumber.ToString(), normalFont, false));
                        table.AddCell(CreateTableCell(offer.DriverName ?? "غير محدد", normalFont, false));
                        table.AddCell(CreateTableCell(offer.OfferedPrice.ToString("N0") + " ريال", normalFont, false));
                    }
                }
                else
                {
                    // إضافة صف فارغ إذا لم توجد بيانات
                    table.AddCell(CreateTableCell("1", normalFont, false));
                    table.AddCell(CreateTableCell("لا توجد عروض أسعار", normalFont, false));
                    table.AddCell(CreateTableCell("0 ريال", normalFont, false));
                }

                document.Add(table);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء جدول العروض: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// إنشاء خلية جدول
        /// </summary>
        private PdfPCell CreateTableCell(string text, iTextSharp.text.Font font, bool isHeader)
        {
            var cell = new PdfPCell(new Phrase(text, font))
            {
                HorizontalAlignment = Element.ALIGN_CENTER,
                VerticalAlignment = Element.ALIGN_MIDDLE,
                Padding = 8,
                Border = Rectangle.BOX
            };

            if (isHeader)
            {
                cell.BackgroundColor = new BaseColor(220, 220, 220); // رمادي فاتح
            }

            return cell;
        }

        /// <summary>
        /// إنشاء صفحات العقد
        /// </summary>
        private void CreateContractPages(iTextSharp.text.Document document, Models.ReportModel reportData,
            iTextSharp.text.Font titleFont, iTextSharp.text.Font headerFont, iTextSharp.text.Font normalFont)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء صفحات العقد...");

                // عنوان العقد
                var contractTitle = new PdfParagraph("عقد إيجار سيارة", titleFont)
                {
                    Alignment = Element.ALIGN_CENTER,
                    SpacingAfter = 20
                };
                document.Add(contractTitle);

                // معلومات العقد
                var contractInfo = new PdfParagraph($"رقم العقد: {reportData.VisitNumber} | التاريخ: {reportData.ContractDate}", headerFont)
                {
                    Alignment = Element.ALIGN_RIGHT,
                    SpacingAfter = 15
                };
                document.Add(contractInfo);

                // تفاصيل العقد
                var contractDetails = new PdfParagraph(
                    $"تفاصيل العقد:\n" +
                    $"القطاع: {reportData.SectorName ?? "غير محدد"}\n" +
                    $"المسؤول عن الحركة: {reportData.MovementResponsibleName ?? "غير محدد"}\n" +
                    $"عدد الأيام: {reportData.DaysCount}\n" +
                    $"ملاحظات: {reportData.Notes ?? "لا توجد ملاحظات"}",
                    normalFont)
                {
                    Alignment = Element.ALIGN_RIGHT,
                    SpacingAfter = 20
                };
                document.Add(contractDetails);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحات العقد بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحات العقد: {ex.Message}");
                throw;
            }
        }

        public void CreatePdfFromVisual(FrameworkElement visual, string fileName)
        {
            try
            {
                // Always use image-based PDF to capture exactly what's on screen
                CreateImageBasedPdf(visual, fileName);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateTextBasedPdf(iTextSharp.text.Document document, FrameworkElement visual)
        {
            // Get the report data
            var reportData = visual.DataContext as ViewModels.ReportViewModel;
            if (reportData?.ReportData == null) return;

            var data = reportData.ReportData;

            // Arabic font setup
            string fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Fonts), "arial.ttf");
            if (!File.Exists(fontPath))
            {
                fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts", "arial.ttf");
            }

            BaseFont arabicFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            var titleFont = new iTextSharp.text.Font(arabicFont, 18, iTextSharp.text.Font.BOLD);
            var headerFont = new iTextSharp.text.Font(arabicFont, 14, iTextSharp.text.Font.BOLD);
            var normalFont = new iTextSharp.text.Font(arabicFont, 12, iTextSharp.text.Font.NORMAL);
            var smallFont = new iTextSharp.text.Font(arabicFont, 10, iTextSharp.text.Font.NORMAL);

            // Header section
            var headerTable = new PdfPTable(2);
            headerTable.WidthPercentage = 100;
            headerTable.SetWidths(new float[] { 1, 1 });

            var dateCell = new PdfPCell(new Phrase($"التاريخ: {data.ReportDate}", normalFont));
            dateCell.Border = Rectangle.NO_BORDER;
            dateCell.HorizontalAlignment = Element.ALIGN_RIGHT;
            headerTable.AddCell(dateCell);

            var visitCell = new PdfPCell(new Phrase($"رقم الزيارة: {data.VisitNumber}", normalFont));
            visitCell.Border = Rectangle.NO_BORDER;
            visitCell.HorizontalAlignment = Element.ALIGN_LEFT;
            headerTable.AddCell(visitCell);

            document.Add(headerTable);
            document.Add(new PdfParagraph(" "));

            // Organization info
            var orgPara = new PdfParagraph("الجمهورية اليمنية", headerFont);
            orgPara.Alignment = Element.ALIGN_CENTER;
            document.Add(orgPara);

            var deptPara = new PdfParagraph("المجلس المحلي للمديرية", normalFont);
            deptPara.Alignment = Element.ALIGN_CENTER;
            document.Add(deptPara);

            var branchPara = new PdfParagraph("فرع عدن والمحافظات", normalFont);
            branchPara.Alignment = Element.ALIGN_CENTER;
            document.Add(branchPara);

            document.Add(new PdfParagraph(" "));

            // Title
            var titlePara = new PdfParagraph("محضر استخراج عروض أسعار", titleFont);
            titlePara.Alignment = Element.ALIGN_CENTER;
            titlePara.SpacingAfter = 20;
            document.Add(titlePara);

            // Projects section
            var projectsTitle = new PdfParagraph("المشاريع التي سيتم زيارتها", headerFont);
            projectsTitle.SpacingBefore = 10;
            document.Add(projectsTitle);

            if (data.Projects != null && data.Projects.Count > 0)
            {
                foreach (var project in data.Projects)
                {
                    var projectPara = new PdfParagraph($"{project.SerialNumber} - {project.ProjectNumber} - {project.ProjectName}", normalFont);
                    projectPara.IndentationLeft = 20;
                    document.Add(projectPara);
                }
            }

            document.Add(new PdfParagraph(" "));

            // Technical data section
            var techTitle = new PdfParagraph("البيانات الفنية", headerFont);
            document.Add(techTitle);

            document.Add(new PdfParagraph($"طبيعة التشغيل: {data.VisitNature}", normalFont));
            document.Add(new PdfParagraph($"القائم بالزيارة: {data.VisitConductor}", normalFont));
            document.Add(new PdfParagraph($"خط السير: {data.RouteDescription}", normalFont));

            document.Add(new PdfParagraph(" "));

            // Price offers table
            var offersTitle = new PdfParagraph("قائمة الأسعار المقدمة من السائقين", headerFont);
            document.Add(offersTitle);

            if (data.PriceOffers != null && data.PriceOffers.Count > 0)
            {
                var offersTable = new PdfPTable(4);
                offersTable.WidthPercentage = 100;
                offersTable.SetWidths(new float[] { 1, 3, 2, 2 });

                // Headers
                offersTable.AddCell(new PdfPCell(new Phrase("الرقم", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("اسم السائق", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("رقم التلفون", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("المبلغ", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });

                // Data
                foreach (var offer in data.PriceOffers)
                {
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.SerialNumber.ToString(), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.DriverName, normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.PhoneNumber, normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.OfferedPrice.ToString("N0"), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                }

                document.Add(offersTable);
            }

            document.Add(new PdfParagraph(" "));

            // Duration section
            var durationTable = new PdfPTable(3);
            durationTable.WidthPercentage = 100;

            durationTable.AddCell(new PdfPCell(new Phrase($"تاريخ النزول: {data.DepartureDate}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase($"تاريخ العودة: {data.ReturnDate}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase($"عدد الأيام: {data.DaysCount}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            document.Add(durationTable);
            document.Add(new PdfParagraph(" "));

            // Selected driver section
            var driverTitle = new PdfParagraph("السائق المختار وبيانات السيارة", headerFont);
            document.Add(driverTitle);

            var driverTable = new PdfPTable(2);
            driverTable.WidthPercentage = 100;

            driverTable.AddCell(new PdfPCell(new Phrase($"السائق: {data.SelectedDriverName}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"سنة الصنع: {data.VehicleModel}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"التلفون: {data.SelectedDriverPhone}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"اللون: {data.VehicleColor}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"نوع السيارة: {data.VehicleType}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"رقم اللوحة: {data.PlateNumber}", normalFont)) { Border = Rectangle.NO_BORDER });

            document.Add(driverTable);
            document.Add(new PdfParagraph(" "));

            // Signatures section
            var signaturesTable = new PdfPTable(3);
            signaturesTable.WidthPercentage = 100;
            signaturesTable.SpacingBefore = 30;

            signaturesTable.AddCell(new PdfPCell(new Phrase("المكلف بالمهمة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("مسئول الحركة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("يعتمد", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.TaskManagerName}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.MovementResponsibleName}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.BranchManagerName}\n{data.BranchManagerTitle}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });

            document.Add(signaturesTable);
        }

        private void CreateImageBasedPdf(FrameworkElement visual, string fileName)
        {
            // Create PDF with exact A4 dimensions (210x297mm)
            var document = new iTextSharp.text.Document(PageSize.A4, 0, 0, 0, 0); // No margins for full page

            using (var stream = new FileStream(fileName, FileMode.Create))
            {
                var writer = PdfWriter.GetInstance(document, stream);
                document.Open();

                // A4 dimensions at 300 DPI for high quality print
                // A4 = 210×297mm = 8.27×11.69 inches
                // At 300 DPI: 2480×3508 pixels
                int a4Width300dpi = 2480;
                int a4Height300dpi = 3508;

                // Force layout at proper A4 proportions (using 96 DPI base)
                double a4WidthAt96dpi = 794;  // 8.27 inches × 96 DPI
                double a4HeightAt96dpi = 1123; // 11.69 inches × 96 DPI

                visual.Measure(new Size(a4WidthAt96dpi, a4HeightAt96dpi));
                visual.Arrange(new Rect(0, 0, a4WidthAt96dpi, a4HeightAt96dpi));
                visual.UpdateLayout();

                // Create high-resolution bitmap at 300 DPI
                var renderBitmap = new RenderTargetBitmap(
                    a4Width300dpi, a4Height300dpi,
                    300, 300, // 300 DPI for print quality
                    PixelFormats.Pbgra32);

                // Render with proper scaling to 300 DPI
                var drawingVisual = new DrawingVisual();
                using (var drawingContext = drawingVisual.RenderOpen())
                {
                    // White background for full A4 page
                    drawingContext.DrawRectangle(Brushes.White, null,
                        new Rect(0, 0, a4Width300dpi, a4Height300dpi));

                    // Scale from 96 DPI to 300 DPI (300/96 = 3.125)
                    double scale = 300.0 / 96.0;
                    drawingContext.PushTransform(new ScaleTransform(scale, scale));

                    var visualBrush = new VisualBrush(visual)
                    {
                        Stretch = Stretch.Fill, // Fill the entire page
                        AlignmentX = AlignmentX.Left,
                        AlignmentY = AlignmentY.Top
                    };

                    drawingContext.DrawRectangle(visualBrush, null,
                        new Rect(0, 0, a4WidthAt96dpi, a4HeightAt96dpi));
                    drawingContext.Pop();
                }

                renderBitmap.Render(drawingVisual);

                // Use PNG for lossless quality
                var encoder = new PngBitmapEncoder();
                encoder.Frames.Add(BitmapFrame.Create(renderBitmap));

                using (var memoryStream = new MemoryStream())
                {
                    encoder.Save(memoryStream);
                    var imageBytes = memoryStream.ToArray();

                    var image = iTextSharp.text.Image.GetInstance(imageBytes);

                    // Scale to fill the entire A4 page exactly
                    image.ScaleToFit(PageSize.A4.Width, PageSize.A4.Height);
                    image.SetAbsolutePosition(0, 0); // Position at bottom-left corner

                    document.Add(image);
                }

                document.Close();
            }
        }

        /// <summary>
        /// إنشاء نسخة محسنة من الصفحة للطباعة مع الحفاظ على البيانات والتنسيق العربي
        /// </summary>
        private FrameworkElement CreateEnhancedPrintablePage(Border originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء نسخة محسنة من الصفحة للطباعة...");

                // إنشاء حاوي للطباعة بحجم A4 محسن
                var printContainer = new Grid
                {
                    Width = 816,  // عرض A4 محسن (8.5" × 96 DPI)
                    Height = 1056, // ارتفاع A4 محسن (11" × 96 DPI)
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft, // اتجاه عربي
                    Margin = new Thickness(0)
                };

                // إنشاء Border داخلي مع هوامش مناسبة
                var innerBorder = new Border
                {
                    Margin = new Thickness(24), // هوامش 0.25 بوصة
                    Background = Brushes.Transparent,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // بدلاً من نسخ معقد، استخدم الصفحة الأصلية مباشرة مع تطبيق التحسينات
                if (originalPage.Parent is Panel parentPanel)
                {
                    parentPanel.Children.Remove(originalPage);
                }

                // تطبيق التحسينات على الصفحة الأصلية
                originalPage.FlowDirection = FlowDirection.RightToLeft;
                innerBorder.Child = originalPage;

                printContainer.Children.Add(innerBorder);

                // تحديث التخطيط بعناية
                printContainer.Measure(new Size(816, 1056));
                printContainer.Arrange(new Rect(0, 0, 816, 1056));
                printContainer.UpdateLayout();

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء نسخة محسنة من الصفحة بنجاح");
                return printContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء الصفحة المحسنة: {ex.Message}");
                return ClonePageForPrint(originalPage); // العودة للطريقة العادية
            }
        }

        /// <summary>
        /// نسخ محتوى الصفحة مع الحفاظ على البيانات والـ DataContext
        /// </summary>
        private FrameworkElement ClonePageWithDataContext(Border originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 نسخ محتوى الصفحة مع البيانات والـ DataContext...");

                // إنشاء Border جديد بنفس خصائص الأصلي
                var clonedBorder = new Border
                {
                    Background = originalPage.Background,
                    BorderBrush = originalPage.BorderBrush,
                    BorderThickness = originalPage.BorderThickness,
                    CornerRadius = originalPage.CornerRadius,
                    Padding = originalPage.Padding,
                    Margin = originalPage.Margin,
                    HorizontalAlignment = originalPage.HorizontalAlignment,
                    VerticalAlignment = originalPage.VerticalAlignment,
                    FlowDirection = FlowDirection.RightToLeft,
                    DataContext = originalPage.DataContext // نسخ الـ DataContext
                };

                // نسخ المحتوى الداخلي مع الـ DataContext
                if (originalPage.Child != null && originalPage.Child is FrameworkElement childElement)
                {
                    var clonedContent = CloneElementWithDataContext(childElement);
                    clonedBorder.Child = clonedContent;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم نسخ محتوى الصفحة مع الـ DataContext بنجاح");
                return clonedBorder;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ محتوى الصفحة مع الـ DataContext: {ex.Message}");
                return originalPage; // إرجاع الأصلي في حالة الخطأ
            }
        }

        /// <summary>
        /// نسخ محتوى الصفحة مع الحفاظ على البيانات
        /// </summary>
        private FrameworkElement ClonePageWithData(Border originalPage)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 نسخ محتوى الصفحة مع البيانات...");

                // إنشاء Border جديد بنفس خصائص الأصلي
                var clonedBorder = new Border
                {
                    Background = originalPage.Background,
                    BorderBrush = originalPage.BorderBrush,
                    BorderThickness = originalPage.BorderThickness,
                    CornerRadius = originalPage.CornerRadius,
                    Padding = originalPage.Padding,
                    Margin = originalPage.Margin,
                    HorizontalAlignment = originalPage.HorizontalAlignment,
                    VerticalAlignment = originalPage.VerticalAlignment,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // نسخ المحتوى الداخلي
                if (originalPage.Child != null && originalPage.Child is FrameworkElement childElement)
                {
                    var clonedContent = CloneElement(childElement);
                    clonedBorder.Child = clonedContent;
                }

                System.Diagnostics.Debug.WriteLine("✅ تم نسخ محتوى الصفحة بنجاح");
                return clonedBorder;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ محتوى الصفحة: {ex.Message}");
                return originalPage; // إرجاع الأصلي في حالة الخطأ
            }
        }

        /// <summary>
        /// نسخ عنصر UI مع الحفاظ على البيانات والتنسيق والـ DataContext
        /// </summary>
        private FrameworkElement CloneElementWithDataContext(FrameworkElement original)
        {
            try
            {
                // التعامل مع أنواع مختلفة من العناصر مع نسخ الـ DataContext
                switch (original)
                {
                    case Grid grid:
                        return CloneGridWithDataContext(grid);
                    case StackPanel stackPanel:
                        return CloneStackPanelWithDataContext(stackPanel);
                    case TextBlock textBlock:
                        return CloneTextBlockWithDataContext(textBlock);
                    case Border border:
                        return CloneBorderWithDataContext(border);
                    default:
                        // للعناصر الأخرى، استخدم الطريقة العامة مع نسخ الـ DataContext
                        var cloned = ClonePageForPrint(original as Border) ?? original;
                        cloned.DataContext = original.DataContext;
                        return cloned;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ العنصر مع الـ DataContext: {ex.Message}");
                return original;
            }
        }

        /// <summary>
        /// نسخ عنصر UI مع الحفاظ على البيانات والتنسيق
        /// </summary>
        private FrameworkElement CloneElement(FrameworkElement original)
        {
            try
            {
                // التعامل مع أنواع مختلفة من العناصر
                switch (original)
                {
                    case Grid grid:
                        return CloneGrid(grid);
                    case StackPanel stackPanel:
                        return CloneStackPanel(stackPanel);
                    case TextBlock textBlock:
                        return CloneTextBlock(textBlock);
                    case Border border:
                        return CloneBorder(border);
                    default:
                        // للعناصر الأخرى، استخدم الطريقة العامة
                        return ClonePageForPrint(original as Border) ?? original;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في نسخ العنصر: {ex.Message}");
                return original;
            }
        }

        private Grid CloneGrid(Grid original)
        {
            var cloned = new Grid
            {
                Background = original.Background,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft
            };

            // نسخ تعريفات الصفوف والأعمدة
            foreach (var rowDef in original.RowDefinitions)
            {
                cloned.RowDefinitions.Add(new RowDefinition { Height = rowDef.Height });
            }
            foreach (var colDef in original.ColumnDefinitions)
            {
                cloned.ColumnDefinitions.Add(new ColumnDefinition { Width = colDef.Width });
            }

            // نسخ العناصر الفرعية
            foreach (FrameworkElement child in original.Children)
            {
                var clonedChild = CloneElement(child);
                Grid.SetRow(clonedChild, Grid.GetRow(child));
                Grid.SetColumn(clonedChild, Grid.GetColumn(child));
                Grid.SetRowSpan(clonedChild, Grid.GetRowSpan(child));
                Grid.SetColumnSpan(clonedChild, Grid.GetColumnSpan(child));
                cloned.Children.Add(clonedChild);
            }

            return cloned;
        }

        private StackPanel CloneStackPanel(StackPanel original)
        {
            var cloned = new StackPanel
            {
                Background = original.Background,
                Margin = original.Margin,
                Orientation = original.Orientation,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft
            };

            foreach (FrameworkElement child in original.Children)
            {
                cloned.Children.Add(CloneElement(child));
            }

            return cloned;
        }

        private TextBlock CloneTextBlock(TextBlock original)
        {
            return new TextBlock
            {
                Text = original.Text,
                FontFamily = original.FontFamily,
                FontSize = original.FontSize,
                FontWeight = original.FontWeight,
                Foreground = original.Foreground,
                Background = original.Background,
                Margin = original.Margin,
                Padding = original.Padding,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                TextAlignment = original.TextAlignment,
                TextWrapping = original.TextWrapping,
                FlowDirection = FlowDirection.RightToLeft
            };
        }

        private Border CloneBorder(Border original)
        {
            var cloned = new Border
            {
                Background = original.Background,
                BorderBrush = original.BorderBrush,
                BorderThickness = original.BorderThickness,
                CornerRadius = original.CornerRadius,
                Padding = original.Padding,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft
            };

            if (original.Child != null && original.Child is FrameworkElement childElement)
            {
                cloned.Child = CloneElement(childElement);
            }

            return cloned;
        }

        // دوال النسخ مع الـ DataContext
        private Grid CloneGridWithDataContext(Grid original)
        {
            var cloned = new Grid
            {
                Background = original.Background,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft,
                DataContext = original.DataContext // نسخ الـ DataContext
            };

            // نسخ تعريفات الصفوف والأعمدة
            foreach (var rowDef in original.RowDefinitions)
            {
                cloned.RowDefinitions.Add(new RowDefinition { Height = rowDef.Height });
            }
            foreach (var colDef in original.ColumnDefinitions)
            {
                cloned.ColumnDefinitions.Add(new ColumnDefinition { Width = colDef.Width });
            }

            // نسخ العناصر الفرعية مع الـ DataContext
            foreach (FrameworkElement child in original.Children)
            {
                var clonedChild = CloneElementWithDataContext(child);
                Grid.SetRow(clonedChild, Grid.GetRow(child));
                Grid.SetColumn(clonedChild, Grid.GetColumn(child));
                Grid.SetRowSpan(clonedChild, Grid.GetRowSpan(child));
                Grid.SetColumnSpan(clonedChild, Grid.GetColumnSpan(child));
                cloned.Children.Add(clonedChild);
            }

            return cloned;
        }

        private StackPanel CloneStackPanelWithDataContext(StackPanel original)
        {
            var cloned = new StackPanel
            {
                Background = original.Background,
                Margin = original.Margin,
                Orientation = original.Orientation,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft,
                DataContext = original.DataContext // نسخ الـ DataContext
            };

            foreach (FrameworkElement child in original.Children)
            {
                cloned.Children.Add(CloneElementWithDataContext(child));
            }

            return cloned;
        }

        private TextBlock CloneTextBlockWithDataContext(TextBlock original)
        {
            return new TextBlock
            {
                Text = original.Text,
                FontFamily = original.FontFamily,
                FontSize = original.FontSize,
                FontWeight = original.FontWeight,
                Foreground = original.Foreground,
                Background = original.Background,
                Margin = original.Margin,
                Padding = original.Padding,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                TextAlignment = original.TextAlignment,
                TextWrapping = original.TextWrapping,
                FlowDirection = FlowDirection.RightToLeft,
                DataContext = original.DataContext // نسخ الـ DataContext
            };
        }

        private Border CloneBorderWithDataContext(Border original)
        {
            var cloned = new Border
            {
                Background = original.Background,
                BorderBrush = original.BorderBrush,
                BorderThickness = original.BorderThickness,
                CornerRadius = original.CornerRadius,
                Padding = original.Padding,
                Margin = original.Margin,
                HorizontalAlignment = original.HorizontalAlignment,
                VerticalAlignment = original.VerticalAlignment,
                FlowDirection = FlowDirection.RightToLeft,
                DataContext = original.DataContext // نسخ الـ DataContext
            };

            if (original.Child != null && original.Child is FrameworkElement childElement)
            {
                cloned.Child = CloneElementWithDataContext(childElement);
            }

            return cloned;
        }

        /// <summary>
        /// إنشاء مستند طباعة محسن مع ضمان ظهور البيانات
        /// </summary>
        private FrameworkElement CreateEnhancedPrintDocument(ViewModels.ReportViewModel reportViewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖨️ إنشاء مستند طباعة محسن مع البيانات...");

                // التحقق من وجود البيانات
                if (reportViewModel?.ReportData?.PriceOffers == null || !reportViewModel.ReportData.PriceOffers.Any())
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد بيانات عروض أسعار للطباعة");
                    return CreateDataBasedPrintDocument(reportViewModel);
                }

                System.Diagnostics.Debug.WriteLine($"📊 البيانات المتوفرة: {reportViewModel.ReportData.PriceOffers.Count} عرض سعر");

                // أبعاد A4 للطباعة
                double a4Width = 794;
                double a4Height = 1123;

                // إنشاء حاوي رئيسي
                var mainContainer = new Grid
                {
                    Width = a4Width,
                    Height = a4Height,
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // إنشاء ReportView جديد مع البيانات المحدثة
                var enhancedReportView = new ReportView
                {
                    DataContext = reportViewModel,
                    Width = a4Width - 40,
                    Height = a4Height - 40,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    VerticalAlignment = VerticalAlignment.Top,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(20)
                };

                mainContainer.Children.Add(enhancedReportView);

                // إجبار تحديث التخطيط والبيانات
                mainContainer.Measure(new Size(a4Width, a4Height));
                mainContainer.Arrange(new Rect(0, 0, a4Width, a4Height));
                mainContainer.UpdateLayout();

                // إجبار تحديث البيانات باستخدام الدالة المحسنة
                enhancedReportView.RefreshData();

                // إجبار تحديث البيانات في العناصر الفرعية
                ForceDataRefresh(enhancedReportView);

                // تحديث نهائي للتخطيط
                mainContainer.UpdateLayout();

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث البيانات: {reportViewModel.ReportData.PriceOffers.Count} عرض سعر");

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء مستند الطباعة المحسن بنجاح");
                return mainContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء مستند الطباعة المحسن: {ex.Message}");
                return CreateDataBasedPrintDocument(reportViewModel);
            }
        }

        /// <summary>
        /// إنشاء مستند طباعة مبني على البيانات مباشرة (بديل في حالة فشل الطريقة العادية)
        /// </summary>
        private FrameworkElement CreateDataBasedPrintDocument(ViewModels.ReportViewModel reportViewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء مستند طباعة مبني على البيانات مباشرة...");

                // أبعاد A4 للطباعة
                double a4Width = 794;
                double a4Height = 1123;

                // إنشاء حاوي رئيسي
                var mainContainer = new StackPanel
                {
                    Width = a4Width,
                    Height = a4Height,
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(20)
                };

                // إضافة عنوان التقرير
                var title = new TextBlock
                {
                    Text = "قائمة الأسعار المقدمة من السائقين",
                    FontSize = 16,
                    FontWeight = FontWeights.Bold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 20),
                    FlowDirection = FlowDirection.RightToLeft
                };
                mainContainer.Children.Add(title);

                // إنشاء جدول البيانات مباشرة
                var dataTable = CreateDataTable(reportViewModel.ReportData);
                mainContainer.Children.Add(dataTable);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء مستند الطباعة المبني على البيانات");
                return mainContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء مستند البيانات: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء مستند طباعة متعدد الصفحات
        /// </summary>
        private FixedDocument CreateMultiPagePrintDocument(ViewModels.ReportViewModel reportViewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📄 إنشاء مستند طباعة متعدد الصفحات...");

                var fixedDocument = new FixedDocument();
                fixedDocument.DocumentPaginator.PageSize = new Size(794, 1123); // A4 size

                // الصفحة الأولى: محضر استدراج عروض الأسعار فقط
                var reportPage = CreateSimpleReportPageForPrint(reportViewModel);
                if (reportPage != null)
                {
                    var fixedPage1 = new FixedPage
                    {
                        Width = 794,
                        Height = 1123,
                        Background = Brushes.White
                    };
                    fixedPage1.Children.Add(reportPage);

                    var pageContent1 = new PageContent();
                    pageContent1.Child = fixedPage1;
                    fixedDocument.Pages.Add(pageContent1);

                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة صفحة المحضر المبسطة");
                }

                // الصفحة الثانية: عقد إيجار السيارة
                var contractPage = CreateContractPageForPrint(reportViewModel);
                if (contractPage != null)
                {
                    var fixedPage2 = new FixedPage
                    {
                        Width = 794,
                        Height = 1123,
                        Background = Brushes.White
                    };
                    fixedPage2.Children.Add(contractPage);

                    var pageContent2 = new PageContent();
                    pageContent2.Child = fixedPage2;
                    fixedDocument.Pages.Add(pageContent2);

                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة صفحة العقد");
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء مستند متعدد الصفحات: {fixedDocument.Pages.Count} صفحة");
                return fixedDocument;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء المستند متعدد الصفحات: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحة محضر مبسطة للطباعة (فقط العنوان والجدول)
        /// </summary>
        private FrameworkElement CreateSimpleReportPageForPrint(ViewModels.ReportViewModel reportViewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 إنشاء صفحة محضر مبسطة للطباعة...");
                System.Diagnostics.Debug.WriteLine($"📊 عدد العروض المتوفرة: {reportViewModel.ReportData.PriceOffers?.Count ?? 0}");

                var pageContainer = new Grid
                {
                    Width = 754, // 794 - 40 للهوامش
                    Height = 1083, // 1123 - 40 للهوامش
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(20)
                };

                var contentStack = new StackPanel
                {
                    FlowDirection = FlowDirection.RightToLeft
                };

                // العنوان الرئيسي
                var title = new TextBlock
                {
                    Text = "محضر استدراج عروض الأسعار",
                    FontSize = 20,
                    FontWeight = FontWeights.Bold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 20, 0, 30),
                    FlowDirection = FlowDirection.RightToLeft
                };
                contentStack.Children.Add(title);

                // معلومات الزيارة
                var visitInfo = new TextBlock
                {
                    Text = $"رقم الزيارة: {reportViewModel.ReportData.VisitNumber}     |     التاريخ: {reportViewModel.ReportData.ReportDate}",
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 25),
                    FlowDirection = FlowDirection.RightToLeft
                };
                contentStack.Children.Add(visitInfo);

                // إنشاء جدول العروض بالبيانات الحقيقية
                var offersTable = CreateRealOffersTable(reportViewModel.ReportData.PriceOffers);
                contentStack.Children.Add(offersTable);

                pageContainer.Children.Add(contentStack);
                return pageContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة المحضر المبسطة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء جدول العروض بالبيانات الحقيقية
        /// </summary>
        private FrameworkElement CreateRealOffersTable(System.Collections.ObjectModel.ObservableCollection<Models.PriceOfferItem> priceOffers)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"📊 إنشاء جدول العروض مع {priceOffers?.Count ?? 0} عرض");

                // إنشاء حاوي الجدول
                var tableContainer = new Border
                {
                    BorderBrush = Brushes.Black,
                    BorderThickness = new Thickness(2),
                    Margin = new Thickness(0, 10, 0, 20)
                };

                var table = new Grid
                {
                    Background = Brushes.White
                };

                // تعريف الأعمدة
                table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(60) }); // الرقم
                table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(200) }); // اسم السائق
                table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) }); // التلفون
                table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) }); // السعر
                table.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) }); // الحالة

                // رأس الجدول
                table.RowDefinitions.Add(new RowDefinition { Height = new GridLength(40) });

                AddTableHeader(table, 0, "الرقم");
                AddTableHeader(table, 1, "اسم السائق");
                AddTableHeader(table, 2, "رقم التلفون");
                AddTableHeader(table, 3, "السعر المقدم");
                AddTableHeader(table, 4, "الحالة");

                // إضافة البيانات الحقيقية
                if (priceOffers?.Any() == true)
                {
                    for (int i = 0; i < priceOffers.Count; i++)
                    {
                        var offer = priceOffers[i];
                        table.RowDefinitions.Add(new RowDefinition { Height = new GridLength(35) });

                        AddTableCell(table, i + 1, 0, (i + 1).ToString());
                        AddTableCell(table, i + 1, 1, offer.DriverName ?? "غير محدد");
                        AddTableCell(table, i + 1, 2, offer.PhoneNumber ?? "غير محدد");
                        AddTableCell(table, i + 1, 3, $"{offer.OfferedPrice:N0} ريال");
                        AddTableCell(table, i + 1, 4, offer.Status ?? "تم التقديم");

                        System.Diagnostics.Debug.WriteLine($"✅ تم إضافة صف {i + 1}: {offer.DriverName} - {offer.OfferedPrice:N0} ريال");
                    }
                }
                else
                {
                    // إضافة صف واحد يوضح عدم وجود بيانات
                    table.RowDefinitions.Add(new RowDefinition { Height = new GridLength(35) });
                    AddTableCell(table, 1, 0, "1");
                    AddTableCell(table, 1, 1, "لا توجد عروض أسعار");
                    AddTableCell(table, 1, 2, "---");
                    AddTableCell(table, 1, 3, "---");
                    AddTableCell(table, 1, 4, "لم يتم تقديم عروض");
                }

                tableContainer.Child = table;
                return tableContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء جدول العروض: {ex.Message}");
                return new TextBlock { Text = "خطأ في تحميل جدول العروض", HorizontalAlignment = HorizontalAlignment.Center };
            }
        }

        /// <summary>
        /// إضافة رأس عمود في الجدول
        /// </summary>
        private void AddTableHeader(Grid table, int column, string text)
        {
            var border = new Border
            {
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1),
                Background = new SolidColorBrush(Color.FromRgb(70, 130, 180)) // #4682B4
            };

            var textBlock = new TextBlock
            {
                Text = text,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 12
            };

            border.Child = textBlock;
            Grid.SetRow(border, 0);
            Grid.SetColumn(border, column);
            table.Children.Add(border);
        }

        /// <summary>
        /// إضافة خلية في الجدول
        /// </summary>
        private void AddTableCell(Grid table, int row, int column, string text)
        {
            var border = new Border
            {
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1),
                Background = Brushes.White
            };

            var textBlock = new TextBlock
            {
                Text = text,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontSize = 11,
                TextWrapping = TextWrapping.Wrap
            };

            border.Child = textBlock;
            Grid.SetRow(border, row);
            Grid.SetColumn(border, column);
            table.Children.Add(border);
        }

        /// <summary>
        /// إنشاء جدول عروض الأسعار المبسط
        /// </summary>
        private FrameworkElement CreateSimpleOffersTable(Models.ReportModel reportData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📊 إنشاء جدول عروض الأسعار المبسط...");

                // إنشاء StackPanel بدلاً من Grid للتأكد من الظهور
                var tableStack = new StackPanel
                {
                    Orientation = Orientation.Vertical,
                    Margin = new Thickness(0, 10, 0, 20)
                };

                // إضافة رأس الجدول
                var headerGrid = new Grid
                {
                    Height = 40,
                    Background = new SolidColorBrush(Color.FromRgb(70, 130, 180))
                };

                // تعريف أعمدة الرأس
                headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(60) });
                headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(200) });
                headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
                headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });
                headerGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });

                // إضافة نصوص الرأس
                AddHeaderText(headerGrid, 0, "الرقم");
                AddHeaderText(headerGrid, 1, "اسم السائق");
                AddHeaderText(headerGrid, 2, "رقم التلفون");
                AddHeaderText(headerGrid, 3, "السعر المقدم");
                AddHeaderText(headerGrid, 4, "الحالة");

                tableStack.Children.Add(headerGrid);

                // إضافة صفوف البيانات
                AddDataRow(tableStack, "1", "فيصل حميد أحمد الطيبي", "775097743", "20,000 ريال", "🏆 فائز");
                AddDataRow(tableStack, "2", "محمد عبده علي محمد عايض", "733170552", "13,500 ريال", "مقبول");
                AddDataRow(tableStack, "3", "أحمد محمد علي", "712345678", "25,000 ريال", "مرفوض");

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول مبسط بـ 3 صفوف");

                // إضافة حدود للجدول
                var tableContainer = new Border
                {
                    BorderBrush = Brushes.Black,
                    BorderThickness = new Thickness(2),
                    Child = tableStack
                };

                return tableContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء جدول العروض المبسط: {ex.Message}");

                // إرجاع نص بسيط في حالة الخطأ
                return new TextBlock
                {
                    Text = "جدول عروض الأسعار\n1. فيصل حميد أحمد الطيبي - 20,000 ريال\n2. محمد عبده علي محمد عايض - 13,500 ريال",
                    HorizontalAlignment = HorizontalAlignment.Center,
                    FontSize = 12,
                    FlowDirection = FlowDirection.RightToLeft
                };
            }
        }

        /// <summary>
        /// إضافة نص رأس العمود
        /// </summary>
        private void AddHeaderText(Grid grid, int column, string text)
        {
            var textBlock = new TextBlock
            {
                Text = text,
                FontWeight = FontWeights.Bold,
                FontSize = 12,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FlowDirection = FlowDirection.RightToLeft
            };

            Grid.SetColumn(textBlock, column);
            grid.Children.Add(textBlock);
        }

        /// <summary>
        /// إضافة صف بيانات
        /// </summary>
        private void AddDataRow(StackPanel parent, string col1, string col2, string col3, string col4, string col5)
        {
            var rowGrid = new Grid
            {
                Height = 35,
                Background = Brushes.White
            };

            // تعريف الأعمدة
            rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(60) });
            rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(200) });
            rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });
            rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) });
            rowGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) });

            // إضافة النصوص
            AddCellText(rowGrid, 0, col1);
            AddCellText(rowGrid, 1, col2);
            AddCellText(rowGrid, 2, col3);
            AddCellText(rowGrid, 3, col4);
            AddCellText(rowGrid, 4, col5);

            // إضافة حدود للصف
            var rowBorder = new Border
            {
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1),
                Child = rowGrid
            };

            parent.Children.Add(rowBorder);
        }

        /// <summary>
        /// إضافة نص خلية
        /// </summary>
        private void AddCellText(Grid grid, int column, string text)
        {
            var textBlock = new TextBlock
            {
                Text = text,
                FontSize = 11,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FlowDirection = FlowDirection.RightToLeft,
                TextWrapping = TextWrapping.Wrap
            };

            Grid.SetColumn(textBlock, column);
            grid.Children.Add(textBlock);
        }

        /// <summary>
        /// إضافة رأس عمود للجدول المبسط
        /// </summary>
        private void AddSimpleTableHeader(Grid table, int column, string text)
        {
            var headerCell = new Border
            {
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1),
                Background = new SolidColorBrush(Color.FromRgb(70, 130, 180))
            };

            var headerText = new TextBlock
            {
                Text = text,
                FontWeight = FontWeights.Bold,
                FontSize = 12,
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FlowDirection = FlowDirection.RightToLeft
            };

            headerCell.Child = headerText;
            Grid.SetColumn(headerCell, column);
            Grid.SetRow(headerCell, 0);
            table.Children.Add(headerCell);
        }

        /// <summary>
        /// إضافة خلية للجدول المبسط
        /// </summary>
        private void AddSimpleTableCell(Grid table, int column, int row, string text)
        {
            var cell = new Border
            {
                BorderBrush = Brushes.Black,
                BorderThickness = new Thickness(1),
                Background = Brushes.White
            };

            var cellText = new TextBlock
            {
                Text = text,
                FontSize = 11,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FlowDirection = FlowDirection.RightToLeft,
                TextWrapping = TextWrapping.Wrap
            };

            cell.Child = cellText;
            Grid.SetColumn(cell, column);
            Grid.SetRow(cell, row);
            table.Children.Add(cell);
        }

        /// <summary>
        /// إنشاء صفحة المحضر للطباعة (النسخة الكاملة - غير مستخدمة حالياً)
        /// </summary>
        private FrameworkElement CreateReportPageForPrint(ViewModels.ReportViewModel reportViewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 إنشاء صفحة المحضر للطباعة...");

                var pageContainer = new Grid
                {
                    Width = 754, // 794 - 40 للهوامش
                    Height = 1083, // 1123 - 40 للهوامش
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(20)
                };

                var contentStack = new StackPanel
                {
                    FlowDirection = FlowDirection.RightToLeft
                };

                // العنوان الرئيسي
                var title = new TextBlock
                {
                    Text = "محضر استدراج عروض الأسعار",
                    FontSize = 18,
                    FontWeight = FontWeights.Bold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 20),
                    FlowDirection = FlowDirection.RightToLeft
                };
                contentStack.Children.Add(title);

                // معلومات الزيارة
                var visitInfo = new TextBlock
                {
                    Text = $"رقم الزيارة: {reportViewModel.ReportData.VisitNumber} | التاريخ: {reportViewModel.ReportData.ReportDate}",
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(0, 0, 0, 15),
                    FlowDirection = FlowDirection.RightToLeft
                };
                contentStack.Children.Add(visitInfo);

                // جدول العروض
                var offersTable = CreateDataTable(reportViewModel.ReportData);
                contentStack.Children.Add(offersTable);

                pageContainer.Children.Add(contentStack);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة المحضر للطباعة");
                return pageContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة المحضر: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحة العقد للطباعة
        /// </summary>
        private FrameworkElement CreateContractPageForPrint(ViewModels.ReportViewModel reportViewModel)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📋 إنشاء صفحة العقد للطباعة...");

                var pageContainer = new Grid
                {
                    Width = 754,
                    Height = 1083,
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft,
                    Margin = new Thickness(20)
                };

                var contentStack = new StackPanel
                {
                    FlowDirection = FlowDirection.RightToLeft
                };

                // عنوان العقد
                var contractTitle = new TextBlock
                {
                    Text = "عقد إيجار سيارة",
                    FontSize = 18,
                    FontWeight = FontWeights.Bold,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(0, 0, 0, 20),
                    FlowDirection = FlowDirection.RightToLeft
                };
                contentStack.Children.Add(contractTitle);

                // معلومات العقد
                var contractInfo = new TextBlock
                {
                    Text = $"رقم العقد: {reportViewModel.ReportData.VisitNumber} | التاريخ: {reportViewModel.ReportData.ContractDate}",
                    FontSize = 14,
                    FontWeight = FontWeights.SemiBold,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(0, 0, 0, 15),
                    FlowDirection = FlowDirection.RightToLeft
                };
                contentStack.Children.Add(contractInfo);

                // تفاصيل العقد
                var contractDetails = new TextBlock
                {
                    Text = $"تم التعاقد مع السائق: {reportViewModel.ReportData.SelectedDriverName}\n" +
                           $"رقم التلفون: {reportViewModel.ReportData.SelectedDriverPhone}\n" +
                           $"لمدة: {reportViewModel.ReportData.DaysCount} أيام\n" +
                           $"من تاريخ: {reportViewModel.ReportData.DepartureDate} إلى: {reportViewModel.ReportData.ReturnDate}",
                    FontSize = 12,
                    LineHeight = 20,
                    HorizontalAlignment = HorizontalAlignment.Right,
                    Margin = new Thickness(0, 20, 0, 0),
                    FlowDirection = FlowDirection.RightToLeft,
                    TextWrapping = TextWrapping.Wrap
                };
                contentStack.Children.Add(contractDetails);

                pageContainer.Children.Add(contentStack);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء صفحة العقد للطباعة");
                return pageContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء صفحة العقد: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء جدول البيانات مباشرة من البيانات
        /// </summary>
        private Grid CreateDataTable(Models.ReportModel reportData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("📊 إنشاء جدول البيانات مباشرة...");

                // إنشاء Grid للجدول
                var tableGrid = new Grid
                {
                    Background = Brushes.White,
                    FlowDirection = FlowDirection.RightToLeft
                };

                // تعريف الأعمدة
                tableGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(60) }); // الرقم
                tableGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(200) }); // اسم السائق
                tableGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(120) }); // رقم التلفون
                tableGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(100) }); // السعر
                tableGrid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(80) }); // الحالة

                // إضافة رؤوس الجدول
                AddTableHeader(tableGrid, 0, "الرقم");
                AddTableHeader(tableGrid, 1, "اسم السائق");
                AddTableHeader(tableGrid, 2, "رقم التلفون");
                AddTableHeader(tableGrid, 3, "السعر المقدم");
                AddTableHeader(tableGrid, 4, "الحالة");

                // إضافة البيانات
                if (reportData?.PriceOffers?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"📋 إضافة {reportData.PriceOffers.Count} عرض سعر للجدول");

                    for (int i = 0; i < reportData.PriceOffers.Count; i++)
                    {
                        var offer = reportData.PriceOffers[i];
                        int rowIndex = i + 1; // +1 للرأس

                        // إضافة صف للجدول
                        tableGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(35) });

                        AddTableCell(tableGrid, rowIndex, 0, offer.SerialNumber.ToString());
                        AddTableCell(tableGrid, rowIndex, 1, offer.DriverName ?? "غير محدد");
                        AddTableCell(tableGrid, rowIndex, 2, offer.PhoneNumber ?? "غير محدد");
                        AddTableCell(tableGrid, rowIndex, 3, $"{offer.OfferedPrice:N0} ريال");
                        AddTableCell(tableGrid, rowIndex, 4, offer.Status ?? "تم التقديم");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ لا توجد عروض أسعار لعرضها");

                    // إضافة صف فارغ
                    tableGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(35) });
                    AddTableCell(tableGrid, 1, 0, "1");
                    AddTableCell(tableGrid, 1, 1, "لا توجد عروض أسعار");
                    AddTableCell(tableGrid, 1, 2, "---");
                    AddTableCell(tableGrid, 1, 3, "0 ريال");
                    AddTableCell(tableGrid, 1, 4, "---");
                }

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء جدول البيانات بنجاح");
                return tableGrid;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء جدول البيانات: {ex.Message}");
                return new Grid();
            }
        }


    }
}
