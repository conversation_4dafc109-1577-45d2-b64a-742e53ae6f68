using System;
using System.Collections.Generic;
using System.Printing;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Markup;
using DriverManagementSystem.Views;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة طباعة مخصصة لصفحة ReportView
    /// </summary>
    public class ReportViewPrintService
    {
        // أبعاد A4 بوحدة DIP
        private const double A4_WIDTH_DIP = 793.7;   // 21.0 cm
        private const double A4_HEIGHT_DIP = 1122.52; // 29.7 cm
        private const double MARGIN_DIP = 37.8;      // 1.0 cm margins

        /// <summary>
        /// طباعة ReportView مباشرة
        /// </summary>
        public static bool PrintReportView(ReportView reportView, string documentTitle = "تقرير الزيارة الميدانية")
        {
            try
            {
                if (reportView == null)
                {
                    MessageBox.Show("لا يوجد تقرير للطباعة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return false;
                }

                // إنشاء حوار الطباعة
                var printDialog = new PrintDialog();
                
                // تحديد إعدادات A4
                try
                {
                    var a4Size = new PageMediaSize(PageMediaSizeName.ISOA4, 210, 297);
                    printDialog.PrintTicket.PageMediaSize = a4Size;
                    printDialog.PrintTicket.PageOrientation = PageOrientation.Portrait;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"تعذر تحديد حجم A4: {ex.Message}");
                }

                // عرض حوار اختيار الطابعة
                if (printDialog.ShowDialog() == true)
                {
                    // إضافة تشخيص مفصل للبيانات قبل الطباعة
                    System.Diagnostics.Debug.WriteLine("🔍 === بدء تشخيص بيانات الطباعة في ReportViewPrintService ===");

                    // التحقق من نوع البيانات في ReportView
                    if (reportView.DataContext is ViewModels.ReportViewModel reportViewModel)
                    {
                        System.Diagnostics.Debug.WriteLine($"📊 عدد العروض المتوفرة: {reportViewModel.ReportData.PriceOffers?.Count ?? 0}");

                        if (reportViewModel.ReportData.PriceOffers?.Count > 0)
                        {
                            System.Diagnostics.Debug.WriteLine("🔍 تفاصيل العروض المتوفرة:");
                            for (int i = 0; i < reportViewModel.ReportData.PriceOffers.Count; i++)
                            {
                                var offer = reportViewModel.ReportData.PriceOffers[i];
                                System.Diagnostics.Debug.WriteLine($"   {i + 1}. {offer.DriverName} - {offer.PhoneNumber} - {offer.OfferedPrice:N0} ريال - {offer.Status}");
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("⚠️ لا توجد عروض أسعار في البيانات");
                            System.Diagnostics.Debug.WriteLine($"⚠️ رقم الزيارة: {reportViewModel.ReportData.VisitNumber}");
                            System.Diagnostics.Debug.WriteLine($"⚠️ تاريخ التقرير: {reportViewModel.ReportData.ReportDate}");
                        }
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ DataContext غير صحيح: {reportView.DataContext?.GetType().Name ?? "null"}");
                    }

                    System.Diagnostics.Debug.WriteLine("🔍 === انتهاء تشخيص بيانات الطباعة ===");

                    // إنشاء مستند للطباعة
                    var printDocument = CreatePrintDocument(reportView, documentTitle);

                    if (printDocument != null)
                    {
                        // طباعة المستند
                        printDialog.PrintDocument(printDocument.DocumentPaginator, documentTitle);

                        MessageBox.Show("تم إرسال التقرير للطباعة بنجاح", "نجح",
                            MessageBoxButton.OK, MessageBoxImage.Information);
                        return true;
                    }
                    else
                    {
                        MessageBox.Show("فشل في إنشاء مستند الطباعة", "خطأ", 
                            MessageBoxButton.OK, MessageBoxImage.Error);
                        return false;
                    }
                }
                
                return false; // المستخدم ألغى الطباعة
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                return false;
            }
        }

        /// <summary>
        /// إنشاء مستند طباعة من ReportView
        /// </summary>
        private static FixedDocument CreatePrintDocument(ReportView reportView, string title)
        {
            try
            {
                var fixedDocument = new FixedDocument();
                
                // إنشاء نسخة للطباعة
                var printableView = CreatePrintableReportView(reportView);
                
                if (printableView != null)
                {
                    // تقسيم المحتوى إلى صفحات A4
                    var pages = SplitIntoA4Pages(printableView);
                    
                    foreach (var pageContent in pages)
                    {
                        var fixedPage = new FixedPage
                        {
                            Width = A4_WIDTH_DIP,
                            Height = A4_HEIGHT_DIP,
                            Background = Brushes.White
                        };

                        // إضافة المحتوى للصفحة
                        fixedPage.Children.Add(pageContent);
                        
                        // إنشاء PageContent
                        var pageContent2 = new PageContent();
                        ((IAddChild)pageContent2).AddChild(fixedPage);
                        
                        fixedDocument.Pages.Add(pageContent2);
                    }
                }
                
                return fixedDocument;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء مستند الطباعة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء نسخة قابلة للطباعة من ReportView
        /// </summary>
        private static ReportView CreatePrintableReportView(ReportView originalView)
        {
            try
            {
                // إنشاء نسخة جديدة
                var printableView = new ReportView();
                
                // نسخ DataContext
                printableView.DataContext = originalView.DataContext;
                
                // تطبيق إعدادات الطباعة
                printableView.Width = A4_WIDTH_DIP;
                printableView.Height = A4_HEIGHT_DIP;
                printableView.Background = Brushes.White;
                printableView.FlowDirection = FlowDirection.RightToLeft;
                
                // إجبار التحديث
                printableView.Measure(new Size(A4_WIDTH_DIP, A4_HEIGHT_DIP));
                printableView.Arrange(new Rect(0, 0, A4_WIDTH_DIP, A4_HEIGHT_DIP));
                printableView.UpdateLayout();

                // إخفاء الأزرار فقط (الصف الأول)
                HideButtonsOnly(printableView);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء نسخة قابلة للطباعة مع إخفاء الأزرار فقط");
                return printableView;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء النسخة القابلة للطباعة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// تقسيم المحتوى إلى صفحات A4 منفصلة
        /// </summary>
        private static List<FrameworkElement> SplitIntoA4Pages(ReportView reportView)
        {
            var pages = new List<FrameworkElement>();

            try
            {
                // الحصول على المحتوى الأصلي من ReportView
                var originalContent = GetReportContent(reportView);

                if (originalContent != null)
                {
                    // تقسيم المحتوى إلى صفحات منفصلة
                    var separatedPages = SeparateContentIntoPages(originalContent);
                    pages.AddRange(separatedPages);
                }

                // إذا لم توجد صفحات، أنشئ صفحة واحدة بكل المحتوى
                if (pages.Count == 0)
                {
                    var fullPage = CreateFullReportPage(reportView);
                    pages.Add(fullPage);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تقسيم الصفحات: {ex.Message}");

                // إنشاء صفحة خطأ
                var errorPage = CreateErrorPage(ex.Message);
                pages.Add(errorPage);
            }

            return pages;
        }

        /// <summary>
        /// الحصول على محتوى التقرير من ReportView (بدون الأزرار)
        /// </summary>
        private static StackPanel GetReportContent(ReportView reportView)
        {
            try
            {
                // إنشاء نسخة كاملة من ReportView مع إخفاء الأزرار فقط
                var printableView = CreatePrintableReportCopy(reportView);

                // البحث عن StackPanel الرئيسي في النسخة الجديدة
                var mainStackPanel = FindChild<StackPanel>(printableView);
                return mainStackPanel;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في الحصول على محتوى التقرير: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء نسخة كاملة من ReportView مع إخفاء الأزرار فقط
        /// </summary>
        private static FrameworkElement CreatePrintableReportCopy(ReportView originalView)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 إنشاء نسخة كاملة من ReportView للطباعة...");

                // إنشاء نسخة جديدة من ReportView
                var printableView = new ReportView();

                // نسخ DataContext
                printableView.DataContext = originalView.DataContext;

                // تطبيق إعدادات الطباعة
                printableView.Width = A4_WIDTH_DIP - 40;
                printableView.Height = A4_HEIGHT_DIP - 40;
                printableView.Background = Brushes.White;
                printableView.FlowDirection = FlowDirection.RightToLeft;

                // إجبار التحديث
                printableView.Measure(new Size(A4_WIDTH_DIP, A4_HEIGHT_DIP));
                printableView.Arrange(new Rect(0, 0, A4_WIDTH_DIP, A4_HEIGHT_DIP));
                printableView.UpdateLayout();

                // إخفاء الأزرار فقط (الصف الأول)
                HideButtonsOnly(printableView);

                System.Diagnostics.Debug.WriteLine("✅ تم إنشاء نسخة كاملة من ReportView مع إخفاء الأزرار فقط");
                return printableView;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء نسخة ReportView: {ex.Message}");
                return originalView; // إرجاع الأصلي في حالة الخطأ
            }
        }

        /// <summary>
        /// إخفاء الأزرار فقط (طباعة مباشرة، معاينة الطباعة، تحرير العقد)
        /// </summary>
        private static void HideButtonsOnly(FrameworkElement element)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 البحث عن الأزرار لإخفائها...");

                // البحث عن Grid الرئيسي
                var mainGrid = FindChild<Grid>(element);
                if (mainGrid != null)
                {
                    // البحث عن الصف الأول (Grid.Row="0") الذي يحتوي على الأزرار
                    for (int i = 0; i < VisualTreeHelper.GetChildrenCount(mainGrid); i++)
                    {
                        var child = VisualTreeHelper.GetChild(mainGrid, i);

                        if (child is FrameworkElement childElement)
                        {
                            var rowValue = Grid.GetRow(childElement);
                            if (rowValue == 0) // الصف الأول (الأزرار)
                            {
                                childElement.Visibility = Visibility.Collapsed;
                                System.Diagnostics.Debug.WriteLine("✅ تم إخفاء الصف الأول (الأزرار)");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إخفاء الأزرار: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن عنصر فرعي من نوع معين
        /// </summary>
        private static T FindChild<T>(DependencyObject parent) where T : DependencyObject
        {
            if (parent == null) return null;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);

                if (child is T result)
                    return result;

                var childOfChild = FindChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }

            return null;
        }

        /// <summary>
        /// تقسيم المحتوى إلى صفحات منفصلة
        /// </summary>
        private static List<FrameworkElement> SeparateContentIntoPages(StackPanel originalContent)
        {
            var pages = new List<FrameworkElement>();

            try
            {
                // تكرار عبر العناصر الفرعية في StackPanel
                foreach (UIElement child in originalContent.Children)
                {
                    if (child is Border border)
                    {
                        // التحقق من أن Border يحتوي على محتوى صفحة (وليس أزرار)
                        if (IsReportPageBorder(border))
                        {
                            // إنشاء صفحة منفصلة لكل Border (صفحة)
                            var separatePage = CreateSeparatePage(border);
                            if (separatePage != null)
                                pages.Add(separatePage);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تقسيم المحتوى: {ex.Message}");
            }

            return pages;
        }

        /// <summary>
        /// التحقق من أن Border يحتوي على محتوى صفحة تقرير
        /// </summary>
        private static bool IsReportPageBorder(Border border)
        {
            try
            {
                // التحقق من Style أو خصائص Border
                if (border.Style != null)
                {
                    // إذا كان له Style يسمى "PrintPageStyle" فهو صفحة تقرير
                    return true;
                }

                // التحقق من المحتوى الداخلي
                if (border.Child is StackPanel stackPanel)
                {
                    // البحث عن عناصر تدل على أنها صفحة تقرير (مثل جداول أو نصوص)
                    foreach (UIElement child in stackPanel.Children)
                    {
                        if (child is Grid || child is TextBlock || child is Border)
                        {
                            return true;
                        }
                    }
                }

                return false;
            }
            catch
            {
                return true; // في حالة الشك، اعتبرها صفحة تقرير
            }
        }

        /// <summary>
        /// إنشاء صفحة منفصلة من Border
        /// </summary>
        private static FrameworkElement CreateSeparatePage(Border originalBorder)
        {
            try
            {
                // إنشاء صفحة A4 كاملة
                var pageContainer = new Border
                {
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Background = Brushes.White,
                    Padding = new Thickness(20) // هوامش صغيرة للطباعة
                };

                // نسخ محتوى Border الأصلي
                var contentCopy = CloneBorderContent(originalBorder);

                if (contentCopy != null)
                {
                    // تطبيق تحسينات الدقة
                    ApplyHighQualityRendering(contentCopy);

                    // تعيين حجم المحتوى ليملأ الصفحة
                    contentCopy.Width = A4_WIDTH_DIP - 40; // مع مراعاة الهوامش
                    contentCopy.Height = A4_HEIGHT_DIP - 40;
                    contentCopy.HorizontalAlignment = HorizontalAlignment.Stretch;
                    contentCopy.VerticalAlignment = VerticalAlignment.Stretch;

                    pageContainer.Child = contentCopy;
                }

                return pageContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء صفحة منفصلة: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحة المحضر
        /// </summary>
        private static FrameworkElement CreateReportPage(ReportView reportView)
        {
            try
            {
                var pageContainer = new Border
                {
                    Width = A4_WIDTH_DIP,
                    Height = A4_HEIGHT_DIP,
                    Background = Brushes.White,
                    Padding = new Thickness(MARGIN_DIP)
                };

                // إنشاء نسخة من ReportView للصفحة الأولى فقط
                var reportCopy = new ReportView();
                reportCopy.DataContext = reportView.DataContext;

                // تحسين الدقة
                RenderOptions.SetBitmapScalingMode(reportCopy, BitmapScalingMode.HighQuality);
                RenderOptions.SetEdgeMode(reportCopy, EdgeMode.Aliased);
                TextOptions.SetTextFormattingMode(reportCopy, TextFormattingMode.Display);
                TextOptions.SetTextRenderingMode(reportCopy, TextRenderingMode.ClearType);

                var viewbox = new Viewbox
                {
                    Stretch = Stretch.Uniform,
                    StretchDirection = StretchDirection.DownOnly,
                    Child = reportCopy
                };

                pageContainer.Child = viewbox;
                return pageContainer;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء صفحة المحضر: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// إنشاء صفحات العقد
        /// </summary>
        private static List<FrameworkElement> CreateContractPages(ReportView reportView)
        {
            var contractPages = new List<FrameworkElement>();

            try
            {
                // يمكن إضافة منطق لإنشاء صفحات العقد منفصلة هنا
                // حالياً سنتركها فارغة ونركز على صفحة المحضر
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في إنشاء صفحات العقد: {ex.Message}");
            }

            return contractPages;
        }

        /// <summary>
        /// نسخ محتوى Border
        /// </summary>
        private static FrameworkElement CloneBorderContent(Border originalBorder)
        {
            try
            {
                // إنشاء نسخة من Border مع تحسينات الحجم
                var newBorder = new Border
                {
                    Background = originalBorder.Background ?? Brushes.White,
                    BorderBrush = originalBorder.BorderBrush,
                    BorderThickness = originalBorder.BorderThickness,
                    Padding = new Thickness(10), // هوامش داخلية صغيرة
                    Margin = new Thickness(0),
                    FlowDirection = FlowDirection.RightToLeft,
                    HorizontalAlignment = HorizontalAlignment.Stretch,
                    VerticalAlignment = VerticalAlignment.Stretch
                };

                // نسخ المحتوى الداخلي
                if (originalBorder.Child != null)
                {
                    var contentCopy = CloneUIElement(originalBorder.Child);

                    // تحسين حجم المحتوى المنسوخ
                    if (contentCopy is FrameworkElement frameworkElement)
                    {
                        frameworkElement.HorizontalAlignment = HorizontalAlignment.Stretch;
                        frameworkElement.VerticalAlignment = VerticalAlignment.Stretch;

                        // إزالة قيود الحجم الثابت
                        frameworkElement.Width = double.NaN;
                        frameworkElement.Height = double.NaN;
                        frameworkElement.MaxWidth = double.PositiveInfinity;
                        frameworkElement.MaxHeight = double.PositiveInfinity;
                    }

                    newBorder.Child = contentCopy;
                }

                return newBorder;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في نسخ محتوى Border: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// نسخ عنصر UI
        /// </summary>
        private static UIElement CloneUIElement(UIElement original)
        {
            try
            {
                // استخدام XamlWriter و XamlReader لنسخ العنصر
                var xaml = XamlWriter.Save(original);
                var cloned = (UIElement)XamlReader.Parse(xaml);
                return cloned;
            }
            catch
            {
                // في حالة فشل النسخ، إرجاع العنصر الأصلي
                return original;
            }
        }

        /// <summary>
        /// تطبيق تحسينات الدقة العالية
        /// </summary>
        private static void ApplyHighQualityRendering(FrameworkElement element)
        {
            try
            {
                RenderOptions.SetBitmapScalingMode(element, BitmapScalingMode.HighQuality);
                RenderOptions.SetEdgeMode(element, EdgeMode.Aliased);
                TextOptions.SetTextFormattingMode(element, TextFormattingMode.Display);
                TextOptions.SetTextRenderingMode(element, TextRenderingMode.ClearType);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق تحسينات الدقة: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء صفحة كاملة بكل المحتوى (احتياطي)
        /// </summary>
        private static FrameworkElement CreateFullReportPage(ReportView reportView)
        {
            var pageContainer = new Border
            {
                Width = A4_WIDTH_DIP,
                Height = A4_HEIGHT_DIP,
                Background = Brushes.White,
                Padding = new Thickness(20)
            };

            // إنشاء نسخة كاملة من ReportView
            var reportCopy = new ReportView();
            reportCopy.DataContext = reportView.DataContext;

            // تعيين حجم ReportView ليملأ الصفحة
            reportCopy.Width = A4_WIDTH_DIP - 40;
            reportCopy.Height = A4_HEIGHT_DIP - 40;
            reportCopy.HorizontalAlignment = HorizontalAlignment.Stretch;
            reportCopy.VerticalAlignment = VerticalAlignment.Stretch;

            // تطبيق تحسينات الدقة
            ApplyHighQualityRendering(reportCopy);

            // استخدام ScrollViewer للمحتوى الطويل
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Hidden,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Hidden,
                Content = reportCopy,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch
            };

            pageContainer.Child = scrollViewer;
            return pageContainer;
        }

        /// <summary>
        /// إنشاء صفحة خطأ
        /// </summary>
        private static FrameworkElement CreateErrorPage(string errorMessage)
        {
            var errorBorder = new Border
            {
                Width = A4_WIDTH_DIP,
                Height = A4_HEIGHT_DIP,
                Background = Brushes.White,
                BorderBrush = Brushes.Red,
                BorderThickness = new Thickness(2),
                Padding = new Thickness(50)
            };

            var errorText = new TextBlock
            {
                Text = $"خطأ في الطباعة:\n\n{errorMessage}",
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = Brushes.Red,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                TextWrapping = TextWrapping.Wrap,
                TextAlignment = TextAlignment.Center
            };

            errorBorder.Child = errorText;
            return errorBorder;
        }

        /// <summary>
        /// معاينة الطباعة
        /// </summary>
        public static void ShowPrintPreview(ReportView reportView)
        {
            try
            {
                if (reportView == null)
                {
                    MessageBox.Show("لا يوجد تقرير للمعاينة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    return;
                }

                // إنشاء مستند للمعاينة
                var printDocument = CreatePrintDocument(reportView, "معاينة التقرير");
                
                if (printDocument != null)
                {
                    // فتح نافذة المعاينة المتطورة
                    var previewWindow = new ProfessionalPrintPreviewWindow();
                    previewWindow.SetPrintDocument(printDocument);
                    previewWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("فشل في إنشاء معاينة الطباعة", "خطأ", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
